"""
Endpoint pour générer des prompts optimisés avec OpenAI
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional
import logging
from services.llm import make_llm_api_call
from utils.config import config
from auth.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter()

class PromptMessage(BaseModel):
    role: str  # 'user' ou 'assistant'
    content: str

class GeneratePromptRequest(BaseModel):
    messages: List[PromptMessage]

class GeneratePromptResponse(BaseModel):
    message: str
    finalPrompt: Optional[str] = None
    needsMoreInfo: Optional[bool] = None

# Prompt système pour l'assistant générateur de prompts
PROMPT_GENERATOR_SYSTEM = """Tu es un expert en création de prompts pour agents IA. Ton rôle est d'aider les utilisateurs à créer des prompts parfaits et optimisés.

OBJECTIF : Créer des prompts clairs, structurés et efficaces pour des agents IA.

PROCESSUS :
1. **Analyse** : Comprends exactement ce que l'utilisateur veut que son agent fasse
2. **Clarification** : Pose des questions précises si des informations manquent :
   - Quel est le domaine d'expertise ?
   - Quel ton/style adopter ?
   - Quelles sont les contraintes spécifiques ?
   - Quels types de réponses sont attendues ?
   - Y a-t-il des formats particuliers à respecter ?
3. **Génération** : Crée un prompt structuré et optimisé

STRUCTURE D'UN BON PROMPT :
- **Rôle/Identité** : Qui est l'agent
- **Contexte** : Dans quel domaine il opère
- **Tâches** : Ce qu'il doit faire précisément
- **Style** : Comment il doit communiquer
- **Contraintes** : Ce qu'il doit éviter
- **Format** : Structure des réponses attendues

RÈGLES :
- Sois conversationnel et amical
- Pose UNE question à la fois pour ne pas submerger
- Quand tu as assez d'informations, génère le prompt final
- Le prompt final doit être entre 200-500 mots
- Utilise des exemples concrets quand c'est utile

INDICATEUR DE PROMPT FINAL :
Quand tu génères le prompt final, commence ta réponse par "🎯 **PROMPT OPTIMISÉ GÉNÉRÉ**" suivi du prompt.

Réponds toujours en français et sois enthousiaste !"""

@router.post("/generate-prompt", response_model=GeneratePromptResponse)
async def generate_prompt(
    request: GeneratePromptRequest,
    current_user = Depends(get_current_user)
):
    """
    Génère un prompt optimisé en utilisant OpenAI
    """
    try:
        # Vérifier qu'on a une clé OpenAI
        if not config.OPENAI_API_KEY:
            raise HTTPException(
                status_code=500, 
                detail="OpenAI API key not configured"
            )

        # Construire les messages pour OpenAI
        messages = [
            {"role": "system", "content": PROMPT_GENERATOR_SYSTEM}
        ]
        
        # Ajouter l'historique de conversation
        for msg in request.messages:
            messages.append({
                "role": msg.role,
                "content": msg.content
            })

        # Appel à OpenAI
        response = await make_llm_api_call(
            messages=messages,
            model_name="openai/gpt-4o",  # Utiliser GPT-4o pour de meilleurs résultats
            temperature=0.7,  # Un peu de créativité
            max_tokens=800,
        )

        assistant_message = response.choices[0].message.content

        # Détecter si c'est un prompt final
        is_final_prompt = "🎯 **PROMPT OPTIMISÉ GÉNÉRÉ**" in assistant_message
        final_prompt = None
        
        if is_final_prompt:
            # Extraire le prompt final (après le marqueur)
            parts = assistant_message.split("🎯 **PROMPT OPTIMISÉ GÉNÉRÉ**", 1)
            if len(parts) > 1:
                final_prompt = parts[1].strip()
                # Nettoyer le prompt (enlever les markdown si présent)
                if final_prompt.startswith("```"):
                    lines = final_prompt.split('\n')
                    if len(lines) > 2:
                        final_prompt = '\n'.join(lines[1:-1])

        return GeneratePromptResponse(
            message=assistant_message,
            finalPrompt=final_prompt,
            needsMoreInfo=not is_final_prompt
        )

    except Exception as e:
        logger.error(f"Erreur lors de la génération de prompt: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la génération: {str(e)}"
        )
