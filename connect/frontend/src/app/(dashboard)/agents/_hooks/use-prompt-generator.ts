import { createMutationHook } from '@/hooks/use-query';
import { toast } from 'sonner';

interface PromptMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface GeneratePromptRequest {
  messages: PromptMessage[];
}

interface GeneratePromptResponse {
  message: string;
  finalPrompt?: string;
  needsMoreInfo?: boolean;
}

async function generatePrompt(request: GeneratePromptRequest): Promise<GeneratePromptResponse> {
  const response = await fetch('/api/agents/generate-prompt', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || 'Erreur lors de la génération du prompt');
  }

  return response.json();
}

export const useGeneratePrompt = () =>
  createMutationHook(
    generatePrompt,
    {
      onError: (error) => {
        console.error('Erreur génération prompt:', error);
        toast.error(
          error instanceof Error 
            ? error.message 
            : 'Erreur lors de la génération du prompt'
        );
      },
    }
  )();
