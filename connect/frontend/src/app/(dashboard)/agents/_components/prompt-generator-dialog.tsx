'use client';

import React, { useState, useRef, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Wand2, 
  Send, 
  Copy, 
  Check, 
  Loader2, 
  <PERSON>rkles,
  MessageCircle 
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useGeneratePrompt } from '../_hooks/use-prompt-generator';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface PromptGeneratorDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onPromptGenerated?: (prompt: string) => void;
}

export function PromptGeneratorDialog({ 
  isOpen, 
  onOpenChange, 
  onPromptGenerated 
}: PromptGeneratorDialogProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [generatedPrompt, setGeneratedPrompt] = useState<string | null>(null);
  const [copiedPrompt, setCopiedPrompt] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const generatePromptMutation = useGeneratePrompt();

  // Message d'accueil initial
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        role: 'assistant',
        content: `👋 Salut ! Je suis votre assistant pour créer des prompts parfaits.

Décrivez-moi ce que vous voulez que votre agent fasse, et je vais :
• Poser des questions pour clarifier vos besoins
• Générer un prompt optimisé et structuré
• M'assurer qu'il soit parfaitement adapté à votre cas d'usage

Que souhaitez-vous que votre agent accomplisse ?`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, messages.length]);

  // Auto-scroll vers le bas
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [messages]);

  // Focus sur l'input quand le dialog s'ouvre
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || generatePromptMutation.isPending) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');

    try {
      const response = await generatePromptMutation.mutateAsync({
        messages: [...messages, userMessage].map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      });

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.message,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Si un prompt final est généré
      if (response.finalPrompt) {
        setGeneratedPrompt(response.finalPrompt);
      }
    } catch (error) {
      console.error('Erreur lors de la génération:', error);
      toast.error('Erreur lors de la génération du prompt');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyPrompt = async () => {
    if (!generatedPrompt) return;
    
    try {
      await navigator.clipboard.writeText(generatedPrompt);
      setCopiedPrompt(true);
      toast.success('Prompt copié dans le presse-papiers !');
      setTimeout(() => setCopiedPrompt(false), 2000);
    } catch (error) {
      toast.error('Erreur lors de la copie');
    }
  };

  const applyPrompt = () => {
    if (!generatedPrompt) return;
    onPromptGenerated?.(generatedPrompt);
    toast.success('Prompt appliqué avec succès !');
    onOpenChange(false);
  };

  const resetConversation = () => {
    setMessages([]);
    setGeneratedPrompt(null);
    setCopiedPrompt(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl h-[600px] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg">
            <Wand2 className="h-5 w-5 text-blue-600" />
            Générateur de Prompt Parfait
            <Sparkles className="h-4 w-4 text-green-600" />
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex flex-col gap-4">
          {/* Zone de conversation */}
          <ScrollArea ref={scrollAreaRef} className="flex-1 pr-4">
            <div className="space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-3 p-3 rounded-lg",
                    message.role === 'user' 
                      ? "bg-blue-50 dark:bg-blue-950/20 ml-8" 
                      : "bg-green-50 dark:bg-green-950/20 mr-8"
                  )}
                >
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                    message.role === 'user'
                      ? "bg-blue-600 text-white"
                      : "bg-green-600 text-white"
                  )}>
                    {message.role === 'user' ? '👤' : '🤖'}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm text-muted-foreground mb-1">
                      {message.role === 'user' ? 'Vous' : 'Assistant IA'}
                    </div>
                    <div className="text-sm whitespace-pre-wrap">
                      {message.content}
                    </div>
                  </div>
                </div>
              ))}
              
              {generatePromptMutation.isPending && (
                <div className="flex gap-3 p-3 rounded-lg bg-green-50 dark:bg-green-950/20 mr-8">
                  <div className="w-8 h-8 rounded-full bg-green-600 text-white flex items-center justify-center text-sm">
                    🤖
                  </div>
                  <div className="flex-1">
                    <div className="text-sm text-muted-foreground mb-1">Assistant IA</div>
                    <div className="flex items-center gap-2 text-sm">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Génération en cours...
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Prompt généré */}
          {generatedPrompt && (
            <div className="border rounded-lg p-4 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-950/20 dark:to-green-950/20">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-sm flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-green-600" />
                  Prompt Optimisé Généré
                </h4>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={copyPrompt}
                    className="h-8"
                  >
                    {copiedPrompt ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                  <Button
                    size="sm"
                    onClick={applyPrompt}
                    className="h-8 bg-green-600 hover:bg-green-700"
                  >
                    Appliquer
                  </Button>
                </div>
              </div>
              <div className="text-xs bg-white dark:bg-gray-800 rounded p-2 max-h-32 overflow-y-auto">
                {generatedPrompt}
              </div>
            </div>
          )}

          {/* Zone de saisie */}
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Décrivez ce que vous voulez que votre agent fasse..."
              disabled={generatePromptMutation.isPending}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || generatePromptMutation.isPending}
              size="icon"
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>

          {/* Actions */}
          <div className="flex justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={resetConversation}
              className="text-muted-foreground"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              Nouvelle conversation
            </Button>
            <div className="text-xs text-muted-foreground">
              Appuyez sur Entrée pour envoyer
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
